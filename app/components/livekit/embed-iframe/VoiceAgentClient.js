'use client';

import React, { useEffect, useMemo, useState } from 'react';
import {Room, RoomEvent, Track} from 'livekit-client';
import {
  BarVisualizer,
  RoomAudioRenderer,
  RoomContext,
  StartAudio,
  useRoomContext,
  useVoiceAssistant
} from '@livekit/components-react';
import useConnectionDetails from '../../../hooks/use-connection-details';

import { SessionView } from './session-view';
import {TrackToggle} from "../livekit/track-toggle";
import {cn} from "../../../lib/utils";
import {DeviceSelect} from "../livekit/device-select";
import {useAgentControlBar} from "../../../hooks/use-agent-control-bar";
import {useDebugMode} from "../../../hooks/useDebug";

export default function VoiceAgentClient({ appConfig }) {

  const room = useMemo(() => new Room(), []);
  const [sessionStarted, setSessionStarted] = useState(false);
  const { connectionDetails, refreshConnectionDetails } = useConnectionDetails();
  const [currentError, setCurrentError] = useState(null);

  useEffect(() => {
    const onDisconnected = () => {
      setSessionStarted(false);
      refreshConnectionDetails();
    };
    const onMediaDevicesError = (error) => {
      setCurrentError({
        title: 'Encountered an error with your media devices',
        description: `${error.name}: ${error.message}`,
      });
    };
    room.on(RoomEvent.MediaDevicesError, onMediaDevicesError);
    room.on(RoomEvent.Disconnected, onDisconnected);
    return () => {
      room.off(RoomEvent.Disconnected, onDisconnected);
      room.off(RoomEvent.MediaDevicesError, onMediaDevicesError);
    };
  }, [room, refreshConnectionDetails]);

  useEffect(() => {

    if (!sessionStarted) {
      return;
    }
    if (room.state !== 'disconnected') {
      return;
    }
    if (!connectionDetails) {
      return;
    }

    const connect = async () => {
      try {
        await room.connect(connectionDetails.serverUrl, connectionDetails.participantToken);
        await room.localParticipant.setMicrophoneEnabled(true, undefined, {
          preConnectBuffer: appConfig.isPreConnectBufferEnabled,
        });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error) {
        console.error('Error connecting to agent:', error);
        setCurrentError({
          title: 'There was an error connecting to the agent',
          description: `${error.name}: ${error.message}`,
        });
      }
    };
    connect();

    return () => {
      room.disconnect();
    };

  }, [room, sessionStarted, connectionDetails, appConfig.isPreConnectBufferEnabled]);

  const roomCtx = useRoomContext();
  const { state, audioTrack } = useVoiceAssistant();
  const {
    micTrackRef,
    // FIXME do I explicitly ensure only the microphone channel is used?
    visibleControls,
    microphoneToggle,
    handleAudioDeviceChange,
    handleDisconnect,
  } = useAgentControlBar({
    controls: { microphone: true },
    saveUserChoices: true,
  });

  const onLeave = () => {
    handleDisconnect();
  };

  useDebugMode();

  // If the agent hasn't connected after an interval, then show an error - something must not be
  // working
  useEffect(() => {
    if (!sessionStarted) {
      return;
    }

    const timeout = setTimeout(() => {
      if (!isAgentAvailable(state)) {
        const reason =
            state === 'connecting'
                ? 'Agent did not join the room. '
                : 'Agent connected but did not complete initializing. ';

        setCurrentError({
          title: 'Session ended',
          description: <p className="w-full">{reason}</p>,
        });
        roomCtx.disconnect();
      }
    }, 20_000);

    return () => clearTimeout(timeout);
  }, [state, sessionStarted, roomCtx, setCurrentError]);

  function isAgentAvailable(agentState) {
    return ['listening', 'thinking', 'speaking'].includes(agentState);
  }

// SessionViewProps type definition (converted from TypeScript)
// appConfig: object
// disabled: boolean
// sessionStarted: boolean
// onDisplayError: function

  return (
    <div className="relative">

      <div inert={sessionStarted} className="absolute inset-0">
        <div className="flex h-full items-center justify-between gap-4 px-3">
          <button onClick={() => setSessionStarted(true)} className="w-48 font-mono">
            Chat with Agent
          </button>
        </div>
      </div>

      <div
        className="h-full w-full"
      >
        <div className="flex h-full items-center justify-between gap-1 gap-4 pl-3">
          <div className="flex flex-col justify-center">
            <span className="text-sm font-medium">{currentError?.title}</span>
            <span className="text-xs">{currentError?.description}</span>
          </div>
          <button onClick={() => setCurrentError(null)}>
            close
          </button>
        </div>
      </div>

      <RoomContext.Provider value={room}>
        <RoomAudioRenderer />
        <StartAudio label="Start Audio" />

        {/* --- */}

        <div {...(!sessionStarted && { inert: "true" })}>
          <div
              key="control-bar"
          >
            <div aria-label="Voice assistant controls" className="absolute inset-0">
              <div className="flex h-full flex-row items-center justify-between gap-1 px-3">
                <div className="flex gap-1">
                  {visibleControls.microphone && (
                      <div className="flex items-center gap-0">
                        <TrackToggle
                            variant="primary"
                            source={Track.Source.Microphone}
                            pressed={microphoneToggle.enabled}
                            disabled={microphoneToggle.pending}
                            onPressedChange={microphoneToggle.toggle}
                            className="peer/track group/track relative w-auto pr-3 pl-3 md:rounded-r-none md:border-r-0 md:pr-2"
                        >
                          <BarVisualizer
                              barCount={3}
                              trackRef={micTrackRef}
                              options={{ minHeight: 5 }}
                              className="flex h-full w-auto items-center justify-center gap-0.5"
                          >
                      <span
                          className={cn([
                            'h-full w-0.5 origin-center rounded-2xl',
                            'group-data-[state=on]/track:bg-fg1 group-data-[state=off]/track:bg-destructive-foreground',
                            'data-lk-muted:bg-muted',
                          ])}
                      />
                          </BarVisualizer>
                        </TrackToggle>
                        <hr className="bg-separator1 peer-data-[state=off]/track:bg-separatorSerious relative z-10 -mr-px hidden h-4 w-px md:block" />
                        <DeviceSelect
                            size="sm"
                            kind="audioinput"
                            onActiveDeviceChange={handleAudioDeviceChange}
                            className={cn([
                              'pl-2',
                              'peer-data-[state=off]/track:text-destructive-foreground',
                              'hover:text-fg1 focus:text-fg1',
                              'hover:peer-data-[state=off]/track:text-destructive-foreground focus:peer-data-[state=off]/track:text-destructive-foreground',
                              'hidden rounded-l-none md:block',
                            ])}
                        />
                      </div>
                  )}

                  {/* FIXME I need to handle the other channels here? */}
                </div>

                {appConfig.isPreConnectBufferEnabled && (
                    <div className="absolute left-1/2 flex h-full -translate-x-1/2 items-center justify-center gap-2">
                      <BarVisualizer
                          barCount={3}
                          trackRef={audioTrack}
                          options={{ minHeight: 5 }}
                          className="absolute -left-5 flex h-6 w-auto items-center justify-center gap-0.5"
                      >
                  <span
                      className={cn([
                        'h-full w-0.5 origin-center rounded-2xl',
                        'bg-fg1',
                        'data-lk-muted:bg-muted',
                      ])}
                  />
                      </BarVisualizer>

                      <p className="animate-text-shimmer inline-block !bg-clip-text text-sm font-semibold text-transparent">
                        Agent listening
                      </p>
                    </div>
                )}

                {visibleControls.leave && (
                    <button onClick={onLeave} className="font-mono">
                      <span className="hidden uppercase md:inline">End Call</span>
                      <span className="inline uppercase md:hidden">End</span>
                    </button>
                )}
              </div>
            </div>
          </div>
        </div>


      </RoomContext.Provider>

    </div>
  );
}
